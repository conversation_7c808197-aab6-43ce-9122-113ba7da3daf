"""
MLQuant - 机器学习量化交易系统

一个专注于机器学习驱动的量化交易策略开发和优化的专业框架。

主要功能模块:
- data: 数据处理和生成
- strategies: 交易策略实现
- backtest: 回测引擎
- performance: 性能分析
- optimization: ML优化算法
- utils: 工具函数
- generators: 策略生成器

版本: 1.0.0
作者: MLQuant Team
"""

__version__ = "1.0.0"
__author__ = "MLQuant Team"

# 导入核心模块
from . import data
from . import strategies
from . import backtest
from . import performance
from . import optimization
from . import utils
from . import generators

# 导入常用类和函数
from .data.generator import RandomDataGenerator
from .strategies.dual_ma import DualMAStrategy
from .strategies.rsi import RSIStrategy
from .strategies.bollinger import BollingerStrategy
from .backtest.engine import BacktestEngine
from .performance.analyzer import PerformanceAnalyzer
from .optimization.ml_optimizer import MLStrategyOptimizer
from .generators.strategy_generator import StrategyFileGenerator
from .generators.strategy_manager import StrategyManager

__all__ = [
    # 模块
    'data',
    'strategies', 
    'backtest',
    'performance',
    'optimization',
    'utils',
    'generators',
    
    # 核心类
    'RandomDataGenerator',
    'DualMAStrategy',
    'RSIStrategy', 
    'BollingerStrategy',
    'BacktestEngine',
    'PerformanceAnalyzer',
    'MLStrategyOptimizer',
    'StrategyFileGenerator',
    'StrategyManager'
]
